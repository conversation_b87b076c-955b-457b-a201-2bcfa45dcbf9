import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../../core/config/responsive.dart';
import '../../../shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';
import '/src/core/config/json_consts.dart';
import '/src/domain/models/broker_list.dart';
import '/src/domain/models/broker.dart';
import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class BrokersTable extends HookWidget {
  final bool showEditOptions;
  final Function(Broker)? onNavigateToAgentNetwork;

  const BrokersTable({
    super.key,
    this.onNavigateToAgentNetwork,
    this.showEditOptions = false,
  });

  // Method to read broker data and convert to BrokerList model
  List<BrokerList> readBrokerDataFromJson() {
    try {
      // Parse the JSON string.
      final Map<String, dynamic> jsonData = json.decode(
        sampleBrokersDataResponse,
      );

      // Extract salesData array.
      final List<dynamic> brokerDataList = jsonData[brokersDataKey] ?? [];
      return brokerDataList
          .map((json) => BrokerList.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error parsing broker data: $e');
      return <BrokerList>[];
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call the method to read and print sales data from JSON
    final List<BrokerList> brokerData = readBrokerDataFromJson();

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      brokerListNameColumnHeader,
      brokerListContactColumnHeader,
      brokerListEmailColumnHeader,
      brokerListAddressColumnHeader,
      brokerListJoinDateColumnHeader,
      brokerListAgentsColumnHeader,
      brokerListTotalSalesColumnHeader,
    ];

    final sortedBrokers = useState<List<BrokerList>>(brokerData);

    void handleSort(String columnName, bool ascending) {
      final sorted = List<BrokerList>.from(sortedBrokers.value);
      sorted.sort((a, b) {
        dynamic aValue, bValue;

        switch (columnName) {
          case brokerListNameColumnHeader:
            aValue = a.name;
            bValue = b.name;
            break;
          case brokerListContactColumnHeader:
            aValue = a.contact;
            bValue = b.contact;
            break;
          case brokerListEmailColumnHeader:
            aValue = a.email;
            bValue = b.email;
            break;
          case brokerListAddressColumnHeader:
            aValue = a.address;
            bValue = b.address;
            break;
          case brokerListJoinDateColumnHeader:
            aValue = a.joinDate;
            bValue = b.joinDate;
            break;
          case brokerListAgentsColumnHeader:
            aValue = a.agents;
            bValue = b.agents;
            break;
          case brokerListSalesColumnHeader:
            aValue = a.totalSales;
            bValue = b.totalSales;
            break;
          default:
            aValue = '';
            bValue = '';
        }

        final comparison = aValue is num && bValue is num
            ? aValue.compareTo(bValue)
            : aValue is DateTime && bValue is DateTime
            ? aValue.compareTo(bValue)
            : aValue.toString().compareTo(bValue.toString());
        return ascending ? comparison : -comparison;
      });
      sortedBrokers.value = sorted;
    }

    final Size size = MediaQuery.of(context).size;
    final isDesktop = Responsive.isDesktop(context);
    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //Customized table layout
            CustomDataTableWidget<BrokerList>(
              data: sortedBrokers.value,
              title: brokersTab,
              titleIcon: "$iconAssetpath/user.png",
              searchHint: searchHint,
              searchFn: (broker) =>
                  broker.name +
                  broker.contact +
                  broker.email +
                  broker.address +
                  broker.joinDate.toString() +
                  broker.agents.toString() +
                  broker.totalSales.toString(),
              filterColumnNames: [brokerListJoinDateColumnHeader],
              filterValueExtractors: {
                brokerListJoinDateColumnHeader: (broker) =>
                    '${broker.joinDate.day}/${broker.joinDate.month}/${broker.joinDate.year}',
              },
              dateFilterColumns: const [
                brokerListJoinDateColumnHeader, // Join date should use calendar picker
              ],
              columnNames: formattedHeaders,
              cellBuilders: [
                (broker) => broker.name,
                (broker) => broker.contact,
                (broker) => broker.email,
                (broker) => broker.address,
                (broker) =>
                    '${broker.joinDate.day}/${broker.joinDate.month}/${broker.joinDate.year}',
                (broker) => broker.agents.toString(),
                (broker) => '₹${broker.totalSales.toStringAsFixed(2)}',
              ],
              iconCellBuilders: [
                (broker) => TableCellData(
                  text: broker.name,
                  leftIconAsset: "$iconAssetpath/agent_round.png",
                  iconSize: 30,
                ),
                null, // contact - no icon
                null, // email - no icon
                null, // address - no icon
                null, // joinDate - no icon
                null, // agents - no icon
                null, // totalSales - no icon
              ],
              useIconBuilders: [
                true, // name - use icon
                false, // contact - use text
                false, // email - use text
                false, // address - use text
                false, // joinDate - use text
                false, // agents - use text
                false, // totalSales - use text
              ],
              actionBuilders: [
                (context, broker) => ActionButtonEye(
                  onPressed: () => _onBrokerAction(context, broker),
                  isCompact: true,
                  isMobile: false,
                ),
                if (showEditOptions) ...[
                  (context, broker) => ActionButtonEye(
                    onPressed: () => _onBrokerAction(context, broker),
                    isCompact: true,
                    isMobile: false,
                    padding: 8,
                    icon: '$iconAssetpath/table_edit.png',
                  ),
                  (context, broker) => ActionButtonEye(
                    onPressed: () => _onBrokerAction(context, broker),
                    isCompact: true,
                    isMobile: false,
                    padding: 8,
                    icon: '$iconAssetpath/delete.png',
                  ),
                ],
              ],
              mobileCardBuilder: (context, broker) =>
                  _buildMobileBrokerCard(broker, context),
              onSort: handleSort,
              emptyStateMessage: noDataAvailable,
              useMinHeight: isDesktop,
              minHeight: constraints.maxHeight, // Set your desired min height
            ),
          ],
        );
      },
    );
  }

  void _onBrokerAction(BuildContext context, BrokerList brokerList) {
    if (onNavigateToAgentNetwork != null) {
      // Find the corresponding Broker object from brokersListJson
      try {
        final broker = brokersListJson.firstWhere(
          (b) => b.id == brokerList.id || b.name == brokerList.name,
        );
        onNavigateToAgentNetwork!(broker);
      } catch (e) {
        // If no matching broker found, show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Broker not found: ${brokerList.name}')),
        );
      }
    } else {
      // Fallback to showing snackbar if callback is null
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Action clicked for ${brokerList.name}')),
      );
    }
  }

  Widget _buildMobileBrokerCard(BrokerList broker, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(broker.name, style: TextStyle(fontWeight: FontWeight.bold)),
            ],
          ),
          const SizedBox(height: 8),
          Text('$brokerListNameColumnHeader: ${broker.name}'),
          Text('$brokerListContactColumnHeader: ${broker.contact}'),
          Text('$brokerListEmailColumnHeader: ${broker.email}'),
          Text('$brokerListAddressColumnHeader: ${broker.address}'),
          Text(
            '$brokerListJoinDateColumnHeader: ${broker.joinDate.day}/${broker.joinDate.month}/${broker.joinDate.year}',
          ),

          Text('$brokerListAgentsColumnHeader: ${broker.agents}'),
          Text(
            '$brokerListSalesColumnHeader: ₹${broker.totalSales.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onBrokerAction(context, broker),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
